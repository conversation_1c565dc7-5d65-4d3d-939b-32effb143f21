# Use specific Python version for reproducibility
FROM python:3.10.12-slim as base

# Set environment variables for Python optimization
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/src
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libsndfile1 \
    libsndfile1-dev \
    ffmpeg \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r dhruva && useradd -r -g dhruva dhruva

# Set working directory
WORKDIR /src

# Copy and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install "torchaudio==2.4.1"

# Copy application code
COPY . /src

# Set proper permissions (more secure than 777)
RUN chown -R dhruva:dhruva /src
RUN chmod -R 755 /src

# Switch to non-root user
USER dhruva

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Expose port
EXPOSE 8000